.vue-mastery-link {
  background-color: var(--vt-c-bg-soft);
  border-radius: 8px;
  padding: 8px 16px 8px 8px;
  transition: color 0.5s, background-color 0.5s;
}

.vue-mastery-link a {
  display: flex;
  align-items: center;
}

.vue-mastery-link .banner {
  background-color: var(--vt-c-white-soft);
  border-radius: 4px;
  width: 96px;
  height: 56px;
  object-fit: cover;
}

.vue-mastery-link .description {
  flex: 1;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  color: var(--vt-c-text-1);
  margin: 0 0 0 16px;
  transition: color 0.5s;
}

.vue-mastery-link .description span {
  color: var(--vt-c-brand);
}

.vue-mastery-link .logo-wrapper {
  position: relative;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--vt-c-white);
  display: flex;
  justify-content: center;
  align-items: center;
}

.vue-mastery-link .logo-wrapper img {
  width: 25px;
  object-fit: contain;
}

@media (max-width: 576px) {
  .vue-mastery-link .banner {
    width: 56px;
  }

  .vue-mastery-link .description {
    font-size: 12px;
    line-height: 18px;
  }
  .vue-mastery-link .logo-wrapper {
    position: relative;
    width: 32px;
    height: 32px;
  }
}
