# 搜索工具 (Search Tools)

搜索工具是一个智能网络搜索 MCP 工具集，提供了网络搜索、内容获取、结果缓存等功能，帮助用户快速获取互联网信息。

### 常见使用场景

**日常信息搜索:**
- "搜索一下今天的天气"
- "查询北京到上海的距离"
- "找一下最新的新闻"
- "搜索人工智能的最新发展"

**学习研究:**
- "搜索关于量子计算的资料"
- "查一下机器学习的基本概念"
- "找一些Python编程教程"
- "搜索历史上的今天发生了什么"

**购物比价:**
- "搜索iPhone 15的价格"
- "查询笔记本电脑推荐"
- "找一些性价比高的手机"
- "搜索最新的促销活动"

**生活服务:**
- "搜索附近的餐厅"
- "查询火车票预订信息"
- "找一下装修公司"
- "搜索周末活动推荐"

**技术问题:**
- "搜索Python错误解决方案"
- "查询API接口文档"
- "找一下软件安装教程"
- "搜索代码示例"

### 使用提示

1. **明确搜索意图**: 清楚描述您要搜索的内容
2. **使用关键词**: 提供准确的关键词有助于获得更好的结果
3. **指定数量**: 可以要求返回特定数量的搜索结果
4. **深入了解**: 可以要求获取特定网页的详细内容
5. **多次搜索**: 可以基于搜索结果进行进一步的搜索

AI 助手会根据您的需求自动调用搜索工具，为您提供准确的网络信息。

## 功能概览

### 网络搜索功能
- **必应搜索**: 基于必应搜索引擎的智能搜索
- **多语言支持**: 支持中文、英文等多种语言搜索
- **区域设置**: 支持不同地区的搜索结果
- **结果数量控制**: 可以设置返回结果的数量

### 内容获取功能
- **网页内容抓取**: 获取搜索结果页面的详细内容
- **内容长度控制**: 可以限制获取内容的长度
- **智能提取**: 自动提取网页的主要内容
- **格式化输出**: 以易读的格式返回内容

### 缓存管理功能
- **搜索结果缓存**: 自动缓存搜索结果
- **会话管理**: 支持多个搜索会话
- **缓存查询**: 可以查看历史搜索结果
- **缓存清理**: 支持清空搜索缓存

### 会话管理功能
- **会话跟踪**: 跟踪搜索会话状态
- **会话信息**: 提供会话的详细信息
- **会话切换**: 支持多个搜索会话的切换
- **会话持久化**: 保存搜索会话数据

## 工具列表

### 1. 搜索工具

#### search_bing - 必应搜索
执行必应搜索，获取网络信息。

**参数:**
- `query` (必需): 搜索关键词
- `num_results` (可选): 返回结果数量，默认5，最大10
- `language` (可选): 搜索语言，默认"zh-cn"
- `region` (可选): 搜索区域，默认"CN"

**使用场景:**
- 日常信息搜索
- 学习研究
- 新闻查询
- 技术问题解决

### 2. 内容获取工具

#### fetch_webpage_content - 获取网页内容
获取指定搜索结果的网页详细内容。

**参数:**
- `result_id` (必需): 搜索结果ID
- `max_length` (可选): 最大内容长度，默认8000，最大20000

**使用场景:**
- 深入阅读网页内容
- 获取文章详情
- 分析网页信息
- 内容研究

### 3. 缓存管理工具

#### get_search_results - 获取搜索结果缓存
获取已缓存的搜索结果。

**参数:**
- `session_id` (可选): 会话ID

**使用场景:**
- 查看历史搜索结果
- 回顾搜索记录
- 会话管理
- 结果对比

#### clear_search_cache - 清空搜索缓存
清空所有搜索缓存数据。

**参数:**
无

**使用场景:**
- 清理搜索记录
- 重置搜索状态
- 释放内存空间
- 隐私保护

### 4. 会话管理工具

#### get_session_info - 获取会话信息
获取当前搜索会话的详细信息。

**参数:**
无

**使用场景:**
- 查看会话状态
- 会话统计
- 系统监控
- 调试信息

## 使用示例

### 基础搜索示例

```python
# 基础搜索
result = await mcp_server.call_tool("search_bing", {
    "query": "人工智能最新发展",
    "num_results": 5
})

# 指定语言和区域的搜索
result = await mcp_server.call_tool("search_bing", {
    "query": "artificial intelligence",
    "num_results": 10,
    "language": "en-us",
    "region": "US"
})

# 获取网页内容
result = await mcp_server.call_tool("fetch_webpage_content", {
    "result_id": "search_result_123",
    "max_length": 10000
})
```

### 缓存管理示例

```python
# 获取搜索结果缓存
result = await mcp_server.call_tool("get_search_results", {})

# 获取特定会话的搜索结果
result = await mcp_server.call_tool("get_search_results", {
    "session_id": "session_123"
})

# 清空搜索缓存
result = await mcp_server.call_tool("clear_search_cache", {})
```

### 会话管理示例

```python
# 获取会话信息
result = await mcp_server.call_tool("get_session_info", {})
```

## 数据结构

### 搜索结果 (SearchResult)
```python
{
    "id": "search_result_123",
    "title": "人工智能的最新发展趋势",
    "url": "https://example.com/ai-trends",
    "snippet": "人工智能技术在2025年取得了重大突破...",
    "source": "example.com",
    "has_content": true,
    "created_at": "2025-01-15T10:30:00Z"
}
```

### 搜索响应 (SearchResponse)
```python
{
    "success": true,
    "query": "人工智能最新发展",
    "num_results": 5,
    "results": [
        {
            "id": "search_result_123",
            "title": "人工智能的最新发展趋势",
            "url": "https://example.com/ai-trends",
            "snippet": "人工智能技术在2025年取得了重大突破...",
            "source": "example.com"
        }
    ],
    "session_info": {
        "session_id": "session_123",
        "created_at": "2025-01-15T10:25:00Z",
        "total_searches": 1,
        "total_results": 5
    }
}
```

### 网页内容 (WebpageContent)
```python
{
    "success": true,
    "result_id": "search_result_123",
    "result_info": {
        "id": "search_result_123",
        "title": "人工智能的最新发展趋势",
        "url": "https://example.com/ai-trends",
        "snippet": "人工智能技术在2025年取得了重大突破...",
        "source": "example.com"
    },
    "content": "人工智能技术在2025年取得了重大突破，包括大语言模型、计算机视觉、自然语言处理等领域...",
    "content_length": 5420
}
```

### 会话信息 (SessionInfo)
```python
{
    "session_id": "session_123",
    "created_at": "2025-01-15T10:25:00Z",
    "last_search_at": "2025-01-15T10:30:00Z",
    "total_searches": 3,
    "total_results": 15,
    "cached_results": 12,
    "status": "active"
}
```

## 搜索技巧

### 1. 关键词选择
- 使用具体、准确的关键词
- 避免过于宽泛的搜索词
- 可以使用多个关键词组合
- 尝试不同的表达方式

### 2. 语言和区域设置
- 中文搜索：language="zh-cn", region="CN"
- 英文搜索：language="en-us", region="US"
- 根据内容来源选择合适的语言
- 区域设置影响搜索结果的相关性

### 3. 结果数量控制
- 一般搜索：5-10个结果
- 深入研究：10个结果
- 快速浏览：3-5个结果
- 避免一次获取过多结果

### 4. 内容获取策略
- 先搜索获得结果列表
- 选择相关性高的结果获取内容
- 根据需要调整内容长度
- 可以获取多个结果的内容进行对比

## 最佳实践

### 1. 搜索策略
- 从宽泛到具体，逐步细化搜索
- 使用多个关键词组合
- 尝试不同的搜索角度
- 关注搜索结果的时效性

### 2. 内容处理
- 根据需要获取网页详细内容
- 合理设置内容长度限制
- 注意内容的来源和可靠性
- 可以获取多个来源的内容进行对比

### 3. 缓存利用
- 充分利用搜索结果缓存
- 定期清理不需要的缓存
- 使用会话管理功能
- 注意缓存的有效性

### 4. 隐私保护
- 敏感搜索后及时清理缓存
- 注意搜索内容的隐私性
- 合理使用会话管理功能
- 避免搜索敏感信息

## 支持的搜索类型

### 信息搜索
- 新闻资讯
- 学术资料
- 百科知识
- 技术文档

### 商业搜索
- 产品信息
- 价格比较
- 商家信息
- 市场分析

### 生活服务
- 本地服务
- 餐饮娱乐
- 交通出行
- 生活指南

### 技术支持
- 编程问题
- 软件使用
- 错误解决
- 技术教程

## 注意事项

1. **网络依赖**: 搜索功能需要稳定的网络连接
2. **搜索限制**: 遵守搜索引擎的使用规范
3. **内容准确性**: 搜索结果的准确性依赖于原始来源
4. **版权问题**: 注意搜索内容的版权和使用限制
5. **隐私保护**: 注意搜索内容的隐私性

## 故障排除

### 常见问题
1. **搜索无结果**: 尝试不同的关键词组合
2. **网页内容获取失败**: 检查网络连接和目标网站状态
3. **搜索速度慢**: 减少搜索结果数量或内容长度
4. **缓存问题**: 清理搜索缓存重新搜索

### 调试方法
1. 检查搜索关键词是否正确
2. 验证网络连接状态
3. 查看会话信息了解搜索状态
4. 使用缓存管理功能排查问题

### 性能优化
1. 合理设置搜索结果数量
2. 根据需要获取网页内容
3. 定期清理搜索缓存
4. 使用会话管理优化搜索流程

通过搜索工具，您可以快速获取互联网上的各种信息，支持学习、工作和生活的各种需求。