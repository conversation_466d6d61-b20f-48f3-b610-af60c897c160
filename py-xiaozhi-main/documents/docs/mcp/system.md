# 系统工具 (System Tools)

系统工具是一个综合性的 MCP 系统管理工具集，提供了系统状态监控、音量控制、设备管理等功能。

### 常见使用场景

**系统状态查询:**
- "查看系统状态"
- "现在系统运行怎么样"
- "检查设备状态"
- "系统有什么问题吗"

**音量控制:**
- "把音量调到50"
- "音量调大一点"
- "设置音量为80"
- "现在音量是多少"

**设备管理:**
- "有多少个IoT设备连接"
- "设备连接状态如何"
- "查看设备列表"
- "设备工作正常吗"

**应用状态:**
- "应用现在是什么状态"
- "系统在忙吗"
- "当前工作模式是什么"
- "应用运行正常吗"

### 使用提示

1. **系统监控**: 定期查看系统状态了解设备健康状况
2. **音量调节**: 可以精确设置音量数值或使用相对调节
3. **设备检查**: 关注IoT设备的连接状态和数量
4. **状态理解**: 了解不同设备状态的含义

AI 助手会根据您的需求自动调用系统工具，为您提供系统管理和监控服务。

## 功能概览

### 系统状态监控
- **完整状态**: 获取系统的完整运行状态
- **音频状态**: 监控音频设备和音量状态
- **应用状态**: 查看应用程序运行状态
- **设备统计**: 统计IoT设备连接情况

### 音量控制功能
- **音量设置**: 精确设置系统音量
- **音量查询**: 获取当前音量级别
- **静音检测**: 检测系统是否处于静音状态
- **音频设备**: 检查音频设备可用性

### 设备管理功能
- **设备状态**: 监控设备运行状态
- **IoT设备**: 管理IoT设备连接
- **设备计数**: 统计连接设备数量
- **状态切换**: 跟踪设备状态变化

### 应用监控功能
- **应用状态**: 监控应用程序状态
- **工作模式**: 识别当前工作模式
- **资源使用**: 监控资源使用情况
- **错误处理**: 检测和报告系统错误

## 工具列表

### 1. 系统状态工具

#### get_system_status - 获取系统状态
获取完整的系统运行状态信息。

**参数:**
无

**使用场景:**
- 系统健康检查
- 故障诊断
- 状态监控
- 性能评估

**返回信息:**
- 音频设备状态
- 应用程序状态
- IoT设备统计
- 系统错误信息

### 2. 音量控制工具

#### set_volume - 设置音量
设置系统音量到指定级别。

**参数:**
- `volume` (必需): 音量级别，范围0-100

**使用场景:**
- 音量调节
- 音频控制
- 环境适应
- 用户偏好设置

**特性:**
- 音量范围验证
- 依赖检查
- 异步执行
- 错误处理

## 使用示例

### 系统状态查询示例

```python
# 获取完整系统状态
result = await mcp_server.call_tool("get_system_status", {})
```

### 音量控制示例

```python
# 设置音量到50
result = await mcp_server.call_tool("set_volume", {
    "volume": 50
})

# 设置音量到最大
result = await mcp_server.call_tool("set_volume", {
    "volume": 100
})

# 设置音量到最小（静音）
result = await mcp_server.call_tool("set_volume", {
    "volume": 0
})
```

## 数据结构

### 系统状态 (SystemStatus)
```python
{
    "audio_speaker": {
        "volume": 75,
        "muted": false,
        "available": true
    },
    "application": {
        "device_state": "IDLE",
        "iot_devices": 3
    },
    "cpu_usage": 25.5,
    "memory_usage": 60.2,
    "disk_usage": 45.8,
    "network_status": "connected",
    "timestamp": "2024-01-15T10:30:00Z"
}
```

### 音频状态 (AudioStatus)
```python
{
    "volume": 75,
    "muted": false,
    "available": true,
    "device_name": "默认扬声器",
    "sample_rate": 44100,
    "channels": 2
}
```

### 应用状态 (ApplicationStatus)
```python
{
    "device_state": "IDLE",
    "iot_devices": 3,
    "uptime": "2小时30分钟",
    "last_activity": "2024-01-15T10:28:00Z",
    "active_tasks": 2
}
```

### 错误状态 (ErrorStatus)
```python
{
    "error": "音量控制依赖不完整",
    "audio_speaker": {
        "volume": 50,
        "muted": false,
        "available": false,
        "reason": "Dependencies not available"
    },
    "application": {
        "device_state": "unknown",
        "iot_devices": 0
    }
}
```

## 设备状态说明

### 应用状态类型
- **IDLE**: 空闲状态，等待用户输入
- **LISTENING**: 正在监听语音输入
- **SPEAKING**: 正在播放语音输出
- **CONNECTING**: 正在连接服务
- **PROCESSING**: 正在处理请求
- **ERROR**: 出现错误状态

### 音频设备状态
- **available**: 音频设备可用
- **volume**: 当前音量级别 (0-100)
- **muted**: 是否处于静音状态
- **device_name**: 音频设备名称

### IoT设备状态
- **connected**: 已连接的设备数量
- **device_types**: 设备类型统计
- **last_update**: 最后更新时间
- **health_status**: 设备健康状态

## 系统监控指标

### 性能指标
- **CPU使用率**: 系统CPU占用百分比
- **内存使用率**: 系统内存占用百分比
- **磁盘使用率**: 磁盘空间占用百分比
- **网络状态**: 网络连接状态

### 应用指标
- **运行时间**: 应用程序运行时长
- **活跃任务**: 当前活跃任务数量
- **最后活动**: 最后一次活动时间
- **错误计数**: 错误发生次数

### 设备指标
- **连接设备**: 已连接IoT设备数量
- **设备类型**: 不同类型设备统计
- **设备健康**: 设备健康状态评估
- **连接质量**: 设备连接质量评估

## 音量控制机制

### 音量设置流程
1. **参数验证**: 验证音量值范围 (0-100)
2. **依赖检查**: 检查音量控制依赖是否可用
3. **异步执行**: 在线程池中执行音量设置
4. **状态更新**: 更新系统音量状态
5. **结果返回**: 返回设置结果

### 跨平台支持
- **Windows**: 使用WASAPI接口
- **macOS**: 使用CoreAudio框架
- **Linux**: 使用ALSA/PulseAudio
- **依赖管理**: 自动检测和加载平台依赖

### 错误处理
- **依赖缺失**: 检测并报告缺失的依赖
- **权限问题**: 处理权限不足的情况
- **设备不可用**: 处理音频设备不可用的情况
- **参数错误**: 验证和处理参数错误

## 最佳实践

### 1. 系统监控
- 定期检查系统状态
- 关注性能指标变化
- 及时处理错误状态
- 监控设备连接状态

### 2. 音量管理
- 设置合适的音量级别
- 考虑使用环境和时间
- 定期检查音频设备状态
- 处理音频设备故障

### 3. 设备管理
- 监控IoT设备连接
- 定期检查设备健康状态
- 处理设备连接问题
- 优化设备性能

### 4. 错误处理
- 及时响应错误状态
- 分析错误原因
- 实施恢复措施
- 记录错误日志

## 故障排除

### 常见问题
1. **音量设置失败**: 检查音频设备和依赖
2. **系统状态异常**: 检查应用程序状态
3. **设备连接问题**: 检查IoT设备连接
4. **权限不足**: 检查系统权限设置

### 调试方法
1. 查看系统状态获取详细信息
2. 检查错误日志和错误信息
3. 验证依赖和权限设置
4. 测试音频设备功能

### 性能优化
1. 定期清理系统缓存
2. 优化IoT设备连接
3. 监控资源使用情况
4. 调整系统参数设置

## 安全考虑

### 权限管理
- 音量控制需要适当权限
- 系统状态访问权限控制
- 设备管理权限验证
- 用户操作权限检查

### 数据保护
- 系统状态信息敏感性
- 设备信息隐私保护
- 操作日志安全存储
- 错误信息脱敏处理

### 访问控制
- 限制系统功能访问
- 验证用户身份
- 控制操作权限
- 审计操作记录

通过系统工具，您可以有效地监控和管理系统状态，确保设备正常运行和最佳性能。