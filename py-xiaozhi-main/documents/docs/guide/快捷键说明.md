# 快捷键说明

## 概述

py-xiaozhi 提供完整的全局快捷键支持，即使在后台运行时也可以快速操作。快捷键系统基于 pynput 库实现，支持跨平台使用。

## 默认快捷键

| 功能分类 | 快捷键 | 功能描述 | 使用场景 |
|---------|-------|---------|----------|
| **语音交互** | `Ctrl+J` | 按住说话模式 | 按住期间录音，松开发送 |
| | `Ctrl+K` | 自动对话模式 | 开启/关闭自动语音检测 |
| | `Ctrl+Q` | 中断对话 | 立即停止AI回复 |
| **模式控制** | `Ctrl+M` | 切换交互模式 | 在手动/自动模式间切换 |
| | `Ctrl+W` | 显示/隐藏窗口 | 窗口最小化/还原 |

## 快捷键详细说明

### 语音交互快捷键

#### Ctrl+J - 按住说话
- **功能**: 手动按压模式，按住期间录音
- **使用方法**: 
  1. 按住 `Ctrl+J`
  2. 对着麦克风说话
  3. 松开按键自动发送语音
- **适用场景**: 精确控制录音时间，避免环境噪音干扰

#### Ctrl+K - 自动对话
- **功能**: 开启/关闭自动对话模式
- **使用方法**: 按下 `Ctrl+K` 切换自动对话状态
- **适用场景**: 连续对话、长时间交互

#### Ctrl+Q - 中断对话
- **功能**: 立即停止当前AI语音回复
- **使用方法**: 在AI回复过程中按下 `Ctrl+Q`
- **适用场景**: 需要紧急打断AI回复时

### 模式控制快捷键

#### Ctrl+M - 切换交互模式
- **功能**: 在不同语音交互模式间切换
- **使用方法**: 按下 `Ctrl+M` 循环切换模式
- **模式类型**: 手动按压模式 ↔ 自动对话模式

#### Ctrl+W - 显示/隐藏窗口
- **功能**: 控制主窗口的显示状态
- **使用方法**: 按下 `Ctrl+W` 切换窗口可见性
- **适用场景**: 快速隐藏/显示主界面

## 快捷键配置

### 配置文件位置

快捷键配置存储在 `config/config.json` 文件中：

```json
{
  "SHORTCUTS": {
    "ENABLED": true,
    "MANUAL_PRESS": {
      "modifier": "ctrl",
      "key": "j",
      "description": "按住说话"
    },
    "AUTO_TOGGLE": {
      "modifier": "ctrl",
      "key": "k",
      "description": "自动对话"
    },
    "ABORT": {
      "modifier": "ctrl",
      "key": "q",
      "description": "中断对话"
    },
    "MODE_TOGGLE": {
      "modifier": "ctrl",
      "key": "m",
      "description": "切换模式"
    },
    "WINDOW_TOGGLE": {
      "modifier": "ctrl",
      "key": "w",
      "description": "显示/隐藏窗口"
    }
  }
}
```

### 自定义快捷键

#### 支持的修饰键

- `ctrl` - Ctrl键
- `alt` - Alt键  
- `shift` - Shift键
- `ctrl+alt` - Ctrl+Alt组合
- `ctrl+shift` - Ctrl+Shift组合

#### 支持的主键

- **字母键**: a-z
- **数字键**: 0-9
- **功能键**: f1-f12
- **特殊键**: space, enter, tab, esc

#### 配置示例

```json
{
  "SHORTCUTS": {
    "ENABLED": true,
    "MANUAL_PRESS": {
      "modifier": "alt",
      "key": "space",
      "description": "按住说话"
    },
    "AUTO_TOGGLE": {
      "modifier": "ctrl+shift",
      "key": "a",
      "description": "自动对话"
    }
  }
}
```

## 平台兼容性

### Windows
- **完整支持**: 所有快捷键功能正常
- **权限要求**: 某些情况下可能需要管理员权限
- **注意事项**: 避免与系统快捷键冲突

### macOS
- **权限配置**: 需要授予辅助功能权限
- **配置路径**: 系统偏好设置 → 安全性与隐私 → 隐私 → 辅助功能
- **终端权限**: 如果通过终端运行，需要给终端授权

### Linux
- **用户组**: 需要将用户添加到 input 组
- **配置命令**: `sudo usermod -a -G input $USER`
- **桌面环境**: 支持 X11 和 Wayland

## 故障排除

### 快捷键不响应

#### 检查配置
1. 确认配置文件中 `SHORTCUTS.ENABLED` 为 `true`
2. 检查快捷键配置格式是否正确
3. 验证修饰键和主键的有效性

#### 权限问题
```bash
# macOS: 检查辅助功能权限
系统偏好设置 → 安全性与隐私 → 隐私 → 辅助功能

# Linux: 添加用户到audio组
sudo usermod -a -G input $USER

# Windows: 以管理员权限运行
右键应用程序 → 以管理员身份运行
```

#### 冲突检测
1. 检查是否有其他程序占用相同快捷键
2. 尝试更改为不常用的快捷键组合
3. 关闭可能冲突的应用程序

### 常见错误处理

#### ImportError: No module named 'pynput'
```bash
# 安装pynput库
pip install pynput
```

#### macOS权限被拒绝
```bash
# 检查并重新授权
系统偏好设置 → 安全性与隐私 → 隐私 → 辅助功能
# 移除并重新添加应用程序
```

#### Linux键盘监听失败
```bash
# 重新登录以生效用户组更改
sudo usermod -a -G input $USER
# 注销并重新登录
```

## 高级配置

### 错误恢复机制

系统内置快捷键错误恢复功能：

- **健康检查**: 每30秒检查监听器状态
- **自动重启**: 检测到失效时自动重启
- **错误计数**: 连续错误超限时触发恢复
- **状态清理**: 重启时清理按键状态

### 调试模式

启用详细日志输出：

```python
# 在配置中启用调试日志
import logging
logging.getLogger('pynput').setLevel(logging.DEBUG)
```

### 性能优化

- **按键缓存**: 减少重复按键检测
- **异步处理**: 非阻塞按键事件处理
- **资源管理**: 自动清理监听器资源

## 代码参考

快捷键系统实现位置：

- **主要实现**: `src/views/components/shortcut_manager.py`
- **配置管理**: `src/utils/config_manager.py`
- **应用集成**: `src/application.py`

### 核心方法

```python
# 启动快捷键监听
from src.views.components.shortcut_manager import start_global_shortcuts_async
shortcut_manager = await start_global_shortcuts_async()

# 手动停止监听
await shortcut_manager.stop()
```