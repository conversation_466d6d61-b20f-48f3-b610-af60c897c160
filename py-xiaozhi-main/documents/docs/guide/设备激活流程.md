# 设备激活流程

## 概述

设备激活流程负责验证设备身份并获取必要的配置信息。系统支持v1和v2两种激活协议，v2协议采用基于HMAC-SHA256的安全验证机制。

## 系统初始化阶段

### 阶段划分

系统初始化分为三个主要阶段：

1. **设备指纹阶段**：准备设备身份信息
2. **配置管理阶段**：初始化配置管理器
3. **OTA配置阶段**：获取服务器配置信息

### 第一阶段：设备指纹

通过`DeviceFingerprint`类收集和管理设备身份信息：

- 生成或加载设备序列号（Serial Number）
- 创建或获取HMAC密钥
- 管理本地激活状态
- 获取标准化的MAC地址

设备指纹文件存储在`config/efuse.json`中，包含：
```json
{
  "serial_number": "SN-xxxxxxxxxxxxxxxx",
  "hmac_key": "64位十六进制字符串",
  "mac_address": "xx:xx:xx:xx:xx:xx",
  "activated": false
}
```

### 第二阶段：配置管理

初始化配置管理器并确保关键配置项：

- 初始化CLIENT_ID（UUID格式）
- 从设备指纹设置DEVICE_ID（MAC地址）
- 验证配置完整性

### 第三阶段：OTA配置

向OTA服务器请求配置信息：

**请求URL**: `SYSTEM_OPTIONS.NETWORK.OTA_VERSION_URL`

**请求头**:
```
Device-Id: MAC地址
Client-Id: 客户端UUID
Content-Type: application/json
User-Agent: board_type/app_name-version
Accept-Language: zh-CN
Activation-Version: version (仅v2协议)
```

**请求体**:
```json
{
  "application": {
    "version": "应用版本",
    "elf_sha256": "HMAC密钥"
  },
  "board": {
    "type": "设备类型",
    "name": "应用名称",
    "ip": "本地IP",
    "mac": "MAC地址"
  }
}
```

## 激活状态分析

系统根据本地激活状态和服务器响应进行状态分析：

### 状态组合处理

1. **本地未激活 + 服务器返回激活数据**: 需要激活流程
2. **本地已激活 + 服务器无激活数据**: 设备已激活
3. **本地未激活 + 服务器无激活数据**: 自动修复本地状态
4. **本地已激活 + 服务器返回激活数据**: 状态不一致，可能需要重新激活

### 服务器响应格式

成功获取配置时的响应：
```json
{
  "mqtt": {
    "endpoint": "mqtt服务器地址",
    "client_id": "MQTT客户端ID",
    "username": "用户名",
    "password": "密码",
    "publish_topic": "发布主题"
  },
  "websocket": {
    "url": "WebSocket服务器URL",
    "token": "访问令牌"
  }
}
```

需要激活时的响应：
```json
{
  "activation": {
    "message": "激活提示信息",
    "code": "6位数字验证码",
    "challenge": "用于HMAC签名的随机字符串",
    "timeout_ms": 30000
  }
}
```

## v2协议激活流程

### 激活触发条件

当系统分析发现需要激活时，启动激活流程：

1. 检查设备序列号和HMAC密钥
2. 显示验证码给用户
3. 播放语音提示
4. 执行HMAC签名验证

### HMAC签名计算

使用HMAC-SHA256算法对challenge进行签名：
```python
signature = hmac.new(
    hmac_key.encode('utf-8'),
    challenge.encode('utf-8'),
    hashlib.sha256
).hexdigest()
```

### 激活请求

**请求URL**: `OTA_VERSION_URL + "activate"`

**请求头**:
```
Activation-Version: 2
Device-Id: MAC地址
Client-Id: 客户端UUID
Content-Type: application/json
```

**请求体**:
```json
{
  "Payload": {
    "algorithm": "hmac-sha256",
    "serial_number": "设备序列号",
    "challenge": "服务器challenge",
    "hmac": "HMAC签名"
  }
}
```

### 响应处理

- **HTTP 200**: 激活成功，更新本地激活状态
- **HTTP 202**: 等待用户输入验证码，继续轮询
- **HTTP 4xx**: 激活失败，记录错误信息

### 重试机制

激活请求采用长轮询机制：

- 最大重试次数：60次
- 重试间隔：5秒
- 总超时时间：5分钟
- 每次重试时重新播放验证码提示

## 用户界面集成

### GUI模式

通过`ActivationWindow`类提供图形界面：

- 显示验证码
- 提供激活状态反馈
- 支持取消激活操作
- 异步等待激活完成

### CLI模式

通过`CLIActivation`类提供命令行界面：

- 控制台输出验证码
- 语音播放验证码
- 显示激活进度

## 安全机制

1. **设备唯一标识**: 基于硬件信息生成唯一序列号
2. **HMAC-SHA256签名**: 防止伪造激活请求
3. **验证码验证**: 用户手动输入确认设备所有权
4. **长轮询机制**: 适应网络环境，避免频繁请求

## 配置更新

激活成功后，系统自动更新配置：

- MQTT连接信息
- WebSocket连接信息
- 本地激活状态标记

配置更新完成后，应用程序继续正常启动流程。