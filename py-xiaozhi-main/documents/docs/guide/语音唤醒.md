# 语音唤醒功能

## 概述

py-xiaozhi 集成了基于 **Sherpa-ONNX** 的高精度语音唤醒功能，支持自定义唤醒词和实时检测。使用轻量级关键词检测模型，提供毫秒级响应速度。

## 唤醒词模型

### 模型下载（必需）

**重要说明**: 项目不包含模型文件，需要提前下载配置。

### 官方模型下载地址

- **官方模型列表**: <https://csukuangfj.github.io/sherpa/onnx/kws/pretrained_models/index.html>
- **推荐模型**: `sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01`

### 下载和配置步骤

#### 1. 下载模型包

```bash
# 方法1：直接下载（推荐）
cd /Users/<USER>/Desktop/workspace/py-xiaozhi
wget https://github.com/k2-fsa/sherpa-onnx/releases/download/kws-models/sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01.tar.bz2

# 解压
tar xvf sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01.tar.bz2

# 方法2：使用ModelScope
pip install modelscope
python -c "
from modelscope import snapshot_download
snapshot_download('pkufool/sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01', cache_dir='./models')
"
```

#### 2. 配置模型文件

模型包下载后包含以下文件：

```
sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01/
├── encoder-epoch-12-avg-2-chunk-16-left-64.int8.onnx    # 速度优先
├── encoder-epoch-12-avg-2-chunk-16-left-64.onnx         # 
├── encoder-epoch-99-avg-1-chunk-16-left-64.int8.onnx    # 速度优先 
├── encoder-epoch-99-avg-1-chunk-16-left-64.onnx         # 精度优先
├── decoder-epoch-12-avg-2-chunk-16-left-64.onnx         #
├── decoder-epoch-99-avg-1-chunk-16-left-64.onnx         # 精度优先
├── joiner-epoch-12-avg-2-chunk-16-left-64.int8.onnx     # 速度优先
├── joiner-epoch-12-avg-2-chunk-16-left-64.onnx          #
├── joiner-epoch-99-avg-1-chunk-16-left-64.int8.onnx     # 速度优先
├── joiner-epoch-99-avg-1-chunk-16-left-64.onnx          # 精度优先
├── tokens.txt                    # Token映射表（必需）
├── keywords_raw.txt              # 原始关键词（可选，用于生成）
├── keywords.txt                  # 现成的
├── test_wavs/                    # 测试音频（可选）
├── configuration.json            # 模型元信息（可选）
└── README.md                     # 说明文档（可选）
```

#### 3. 选择配置方案

**方案一：精度优先（推荐）**

```bash
cd sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01

# 复制精度优先的epoch-99 fp32三件套
cp encoder-epoch-99-avg-1-chunk-16-left-64.onnx ../models/encoder.onnx
cp decoder-epoch-99-avg-1-chunk-16-left-64.onnx ../models/decoder.onnx  
cp joiner-epoch-99-avg-1-chunk-16-left-64.onnx ../models/joiner.onnx

# 复制配套文件
cp tokens.txt ../models/tokens.txt
cp keywords_raw.txt ../models/keywords_raw.txt  # 可选
```

**方案二：速度优先**

```bash
cd sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01

# 复制速度优先的epoch-99 int8三件套
cp encoder-epoch-99-avg-1-chunk-16-left-64.int8.onnx ../models/encoder.onnx
cp decoder-epoch-99-avg-1-chunk-16-left-64.onnx ../models/decoder.onnx
cp joiner-epoch-99-avg-1-chunk-16-left-64.int8.onnx ../models/joiner.onnx

# 复制配套文件  
cp tokens.txt ../models/tokens.txt
```

**注意事项**:

- **不要混用fp32与int8**：三个模型文件必须保持一致的精度
- **优先选择epoch-99**：比epoch-12训练更充分，精度更高
- **必需文件**：`encoder.onnx` + `decoder.onnx` + `joiner.onnx` + `tokens.txt` + `keywords.txt`

### 最终模型文件结构

配置完成后，你的models目录应该包含：

```
models/
├── encoder.onnx      # 编码器模型（重命名后）
├── decoder.onnx      # 解码器模型（重命名后） 
├── joiner.onnx       # 连接器模型（重命名后）
├── tokens.txt        # 拼音Token映射表（228行版本）
├── keywords.txt      # 关键词配置文件（需创建）
└── keywords_raw.txt  # 原始关键词文件（可选）
```

### 模型性能对比

| 模型版本 | 文件大小 | 推理速度 | 识别精度 | 资源占用 | 推荐场景 |
|---------|---------|---------|---------|---------|---------|
| **epoch-99 fp32** | ~13MB | 中等 | 最高 | 中等 | **桌面电脑（推荐）** |
| **epoch-99 int8** | ~4MB | 快 | 高 | 低 | 移动设备/资源受限 |
| **epoch-12 fp32** | ~13MB | 中等 | 中高 | 中等 | 一般使用 |
| **epoch-12 int8** | ~4MB | 最快 | 中等 | 最低 | 极速响应需求 |

## 启用语音唤醒

### 配置文件设置

编辑 `config/config.json`：

```json
{
  "WAKE_WORD_OPTIONS": {
    "USE_WAKE_WORD": true,
    "MODEL_PATH": "models",
    "NUM_THREADS": 4,
    "PROVIDER": "cpu",
    "MAX_ACTIVE_PATHS": 2,
    "KEYWORDS_SCORE": 1.8,
    "KEYWORDS_THRESHOLD": 0.2,
    "NUM_TRAILING_BLANKS": 1
  }
}
```

### 配置参数详解

| 参数 | 默认值 | 说明 | 调优建议 |
|------|--------|------|----------|
| `USE_WAKE_WORD` | `true` | 启用语音唤醒功能 | - |
| `MODEL_PATH` | `"models"` | 模型文件目录 | 确保路径正确 |
| `NUM_THREADS` | `4` | 处理线程数 | 电脑性能好可设置6-8 |
| `PROVIDER` | `"cpu"` | 推理引擎 | 可选: cpu, cuda, coreml |
| `MAX_ACTIVE_PATHS` | `2` | 搜索路径数 | 减少提升速度，增加提升准确性 |
| `KEYWORDS_SCORE` | `1.8` | 关键词增强分数 | 提高减少误检，降低提升灵敏度 |
| `KEYWORDS_THRESHOLD` | `0.2` | 检测阈值 | 降低提升灵敏度，提高减少误检 |
| `NUM_TRAILING_BLANKS` | `1` | 尾随空白数量 | 通常保持1 |

## 自定义唤醒词

### 当前支持的唤醒词

```
1. 小爱同学    (x iǎo ài t óng x ué)
2. 你好问问    (n ǐ h ǎo w èn w èn)
3. 小艺小艺    (x iǎo y ì x iǎo y ì)
4. 小米小米    (x iǎo m ǐ x iǎo m ǐ)
5. 你好小智 (n ǐ h ǎo x iǎo zh ì)
6. 贾维斯 (j iā w éi s ī)
```

### 添加新唤醒词

#### 方法1: 编辑关键词文件

编辑 `models/keywords.txt`，按格式添加：

```
# 格式：拼音分解 @中文原文
x iǎo zh ì @小智
n ǐ h ǎo x iǎo zh ì @你好小智
j iā w éi s ī @贾维斯
k āi sh ǐ g ōng z uò @开始工作
```

#### 方法2: 使用拼音转换工具

```python
from pypinyin import lazy_pinyin, Style

def generate_keyword_line(text):
    pinyin_list = lazy_pinyin(text, style=Style.TONE3, neutral_tone_with_five=True)
    processed_pinyin = [py.rstrip('12345') for py in pinyin_list]
    pinyin_str = ' '.join(processed_pinyin)
    return f'{pinyin_str} @{text}'

# 生成新唤醒词
wake_words = ['小助手', '开始工作', '星期五']
for word in wake_words:
    print(generate_keyword_line(word))
```

### 唤醒词选择建议

#### 推荐的唤醒词特点

- **长度适中**: 2-4个字符
- **发音清晰**: 避免相似音混淆
- **独特性强**: 避免日常对话常用词
- **朗朗上口**: 容易记忆和发音

#### 示例好的唤醒词

```
- 你好小智    # 4字，独特，清晰
- 贾维斯      # 3字，独特，科技感
- 开始工作    # 4字，明确意图
- 小助手      # 3字，简单易记
```

#### 避免使用

```
- 嗯         # 太短，容易误触
- 你好       # 太常用
- 请帮我做一个计划 # 太长
- 谢谢       # 日常用语
```

## 使用方法

### 启动流程

1. **启动程序**：

   ```bash
   cd /Users/<USER>/Desktop/workspace/py-xiaozhi
   python main.py
   ```

2. **模型加载**：
   - 系统自动加载Sherpa-ONNX模型
   - 初始化关键词检测器
   - 进入唤醒词监听状态

3. **语音唤醒**：
   - 清晰说出配置的唤醒词
   - 系统自动切换到LISTENING状态
   - 开始语音对话

### 使用技巧

#### 最佳唤醒方式

- **音量适中**: 正常说话音量
- **语速自然**: 不要太快或太慢
- **发音清晰**: 特别注意声调
- **环境安静**: 减少背景噪音

## 性能优化

### 速度优化配置

```json
{
  "WAKE_WORD_OPTIONS": {
    "NUM_THREADS": 6,           // 提高线程数
    "MAX_ACTIVE_PATHS": 1,      // 减少搜索路径
    "KEYWORDS_THRESHOLD": 0.15, // 降低阈值提高灵敏度
    "KEYWORDS_SCORE": 1.5       // 降低分数提升速度
  }
}
```

### 精度优化配置

```json
{
  "WAKE_WORD_OPTIONS": {
    "NUM_THREADS": 4,           // 适中线程数
    "MAX_ACTIVE_PATHS": 3,      // 增加搜索路径
    "KEYWORDS_THRESHOLD": 0.25, // 提高阈值减少误检
    "KEYWORDS_SCORE": 2.2       // 提高分数增强准确性
  }
}
```

### 性能监控

检查当前性能：

```python
# 在应用中查看统计信息
stats = wake_word_detector.get_performance_stats()
print(f"引擎: {stats['engine']}")
print(f"线程数: {stats['num_threads']}")
print(f"检测阈值: {stats['keywords_threshold']}")
print(f"运行状态: {stats['is_running']}")
```

## 故障排除

### 常见问题

#### 1. 唤醒词无响应

**症状**: 说出唤醒词没有反应

**解决方案**:

```bash
# 检查配置
grep -A 10 "WAKE_WORD_OPTIONS" config/config.json

# 检查模型文件
ls -la models/

# 测试功能
python test_new_keywords.py
```

#### 2. 响应速度慢

**症状**: 唤醒词识别延迟大

**解决方案**:

```json
{
  "WAKE_WORD_OPTIONS": {
    "KEYWORDS_THRESHOLD": 0.15,  // 降低阈值
    "NUM_THREADS": 6,            // 增加线程
    "MAX_ACTIVE_PATHS": 1        // 减少搜索路径
  }
}
```

#### 3. 误检频繁

**症状**: 经常误触发唤醒

**解决方案**:

```json
{
  "WAKE_WORD_OPTIONS": {
    "KEYWORDS_THRESHOLD": 0.3,   // 提高阈值
    "KEYWORDS_SCORE": 2.5,       // 提高分数
    "MAX_ACTIVE_PATHS": 3        // 增加搜索路径
  }
}
```

#### 4. 模型加载失败

**症状**: 启动时报模型文件错误

**解决方案**:

```bash
# 检查文件完整性
ls -la models/
file models/*.onnx
file models/tokens.txt

# 重新验证模型
python test_new_keywords.py
```

### 调试命令

```bash
# 查看系统日志
tail -f logs/app.log | grep -i kws

# 监控性能
top -p $(pgrep -f "python main.py")

# 测试音频设备
python -c "import sounddevice as sd; print(sd.query_devices())"
```

## 高级配置

### 环境适配

#### 安静环境（办公室）

```json
{
  "WAKE_WORD_OPTIONS": {
    "KEYWORDS_THRESHOLD": 0.15,
    "KEYWORDS_SCORE": 1.5,
    "MAX_ACTIVE_PATHS": 1
  }
}
```

#### 嘈杂环境（开放空间）

```json
{
  "WAKE_WORD_OPTIONS": {
    "KEYWORDS_THRESHOLD": 0.25,
    "KEYWORDS_SCORE": 2.5,
    "MAX_ACTIVE_PATHS": 3
  }
}
```

### 与AEC集成

语音唤醒与回声消除（AEC）完美集成：

```json
{
  "AEC_OPTIONS": {
    "ENABLED": true,              // AEC为唤醒词提供干净音频
    "ENABLE_PREPROCESS": true     // 噪声抑制提升检测准确性
  },
  "WAKE_WORD_OPTIONS": {
    "USE_WAKE_WORD": true         // 使用AEC处理后的音频
  }
}
```

### 性能基准

在标准配置下的预期性能：

| 指标 | 目标值 | 说明 |
|------|--------|------|
| **响应延迟** | < 1秒 | 从说话到检测完成 |
| **检测准确率** | > 95% | 正确识别设定唤醒词 |
| **误检率** | < 5% | 错误触发频率 |
| **CPU占用** | < 30% | 持续运行时的资源消耗 |
| **内存占用** | < 100MB | 模型和缓冲区内存使用 |

## 总结

**Sherpa-ONNX 语音唤醒功能特点**:

- **高精度**: 基于深度学习的端到端检测
- **低延迟**: 毫秒级响应速度
- **低资源**: 轻量级模型，适合PC运行
- **可定制**: 支持自定义唤醒词
- **易集成**: 与现有音频处理完美融合

现在你可以享受智能、快速、准确的语音唤醒体验了！
