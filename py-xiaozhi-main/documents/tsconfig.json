{"compilerOptions": {"target": "es2017", "module": "esnext", "lib": ["ESNext", "DOM", "DOM.Iterable"], "moduleResolution": "node", "esModuleInterop": true, "strict": true, "strictNullChecks": true, "strictFunctionTypes": true, "declaration": true, "declarationDir": "./types", "resolveJsonModule": true, "rootDir": "./", "baseUrl": ".", "jsx": "preserve", "skipLibCheck": true, "skipDefaultLibCheck": true, "noUnusedLocals": true, "types": ["@types/node"]}, "include": ["**/*", "**/*.mts"], "exclude": ["node_modules", "**/*.md"]}