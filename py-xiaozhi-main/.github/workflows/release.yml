name: GitHub Release

on:
  push:
    tags: ['v*.*.*']

permissions:
  contents: write

jobs:
  create-release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Extract version
        id: version
        run: |
          VERSION=${GITHUB_REF#refs/tags/v}
          echo "version=$VERSION" >> $GITHUB_OUTPUT

      - name: Create Release with Release Drafter
        uses: release-drafter/release-drafter@v6
        with:
          version: ${{ steps.version.outputs.version }}
          publish: true
          footer: |
            
            ## Documentation
            
            - [在线文档](https://huangjunsen0406.github.io/py-xiaozhi/)
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
