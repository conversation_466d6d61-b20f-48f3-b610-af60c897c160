# 电机语音控制命令说明

## 概述

通过AI语音对话可以控制L298N双电机的运动。系统支持方向控制、速度调节和定时运动。

## 支持的语音命令

### 基本运动控制

| 语音命令示例 | 对应方法 | 说明 |
|-------------|----------|------|
| "向前移动" / "前进" | MoveForward | 向前移动，默认速度50% |
| "向后移动" / "后退" | MoveBackward | 向后移动，默认速度50% |
| "向左转" / "左转" | TurnLeft | 左转（左电机后退，右电机前进） |
| "向右转" / "右转" | TurnRight | 右转（左电机前进，右电机后退） |
| "停止运动" / "停止" | StopMovement | 立即停止所有运动 |

### 速度控制

| 语音命令示例 | 对应方法 | 说明 |
|-------------|----------|------|
| "设置速度为30" | SetSpeed | 设置运动速度为30% |
| "速度调到80" | SetSpeed | 设置运动速度为80% |
| "以50的速度前进" | MoveForward | 以指定速度前进 |

### 定时运动

| 语音命令示例 | 对应方法 | 说明 |
|-------------|----------|------|
| "前进2秒" | MoveForward | 前进2秒后自动停止 |
| "左转1.5秒" | TurnLeft | 左转1.5秒后自动停止 |
| "以60的速度后退3秒" | MoveBackward | 指定速度和时间 |

## 命令参数说明

### 速度参数
- 范围：0-100（百分比）
- 默认值：50%
- 0表示停止

### 时间参数
- 单位：秒
- 默认值：0（表示持续运动）
- 大于0时会自动定时停止

## 电机控制逻辑

### 运动方向
- **前进**：两个电机都向前转动
- **后退**：两个电机都向后转动
- **左转**：左电机后退，右电机前进（原地转向）
- **右转**：左电机前进，右电机后退（原地转向）

### 控制特性
1. **独立速度控制**：可以先设置速度，再设置方向
2. **定时自动停止**：指定时间后自动停止运动
3. **即时停止**：任何时候都可以通过"停止运动"立即停止
4. **状态保持**：系统会记住当前的速度和方向状态

## 使用示例

### 基本使用
```
用户: "向前移动"
系统: "向前移动，速度50%（持续运动）"

用户: "停止运动"  
系统: "运动已停止"
```

### 带速度控制
```
用户: "以80的速度向前移动"
系统: "向前移动，速度80%（持续运动）"

用户: "设置速度为30"
系统: "速度设置为30%，方向：forward"
```

### 定时运动
```
用户: "左转2秒"
系统: "向左转，速度50%，持续2秒"
（2秒后自动停止）
```

## 安全注意事项

1. **紧急停止**：随时可以说"停止运动"来紧急停止
2. **定时保护**：建议使用定时运动避免长时间运行
3. **速度限制**：系统会自动限制速度在安全范围内
4. **状态监控**：可以随时查询当前运动状态

## 故障排除

### 常见问题
1. **电机不响应**：检查GPIO连接和电源
2. **运动方向错误**：检查电机接线
3. **速度异常**：检查PWM设置和电源电压
4. **无法停止**：使用"停止运动"命令强制停止

### 调试命令
- 查看状态：可以询问"电机状态如何"
- 测试连接：运行测试脚本验证硬件连接
- 检查日志：查看系统日志了解详细错误信息

## 技术实现

### GPIO连接
- ENA (Enable A) = GPIO 17
- ENB (Enable B) = GPIO 18  
- IN1 = GPIO 27
- IN2 = GPIO 22
- IN3 = GPIO 23
- IN4 = GPIO 24

### 软件架构
- 使用gpiozero库进行电机控制
- 异步处理确保响应性能
- 线程安全的定时器管理
- 完整的错误处理和资源清理

通过这些语音命令，用户可以自然地控制机器人的运动，实现灵活的移动控制。
