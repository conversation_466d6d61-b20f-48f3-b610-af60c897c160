import asyncio
import logging
import threading
import time
from typing import Dict, Any

try:
    from gpiozero import Motor
    GPIOZERO_AVAILABLE = True
except ImportError:
    GPIOZERO_AVAILABLE = False
    # 在非树莓派环境下使用模拟模块
    class MockMotor:
        def __init__(self, forward, backward, enable=None, pwm=True):
            self.forward_pin = forward
            self.backward_pin = backward
            self.enable_pin = enable
            self.pwm = pwm
            self.current_speed = 0
            self.current_direction = "stop"

        def forward(self, speed=1.0):
            self.current_speed = speed
            self.current_direction = "forward"
            print(f"MockMotor (pins {self.forward_pin}/{self.backward_pin}) forward at speed {speed:.2f}")

        def backward(self, speed=1.0):
            self.current_speed = speed
            self.current_direction = "backward"
            print(f"MockMotor (pins {self.forward_pin}/{self.backward_pin}) backward at speed {speed:.2f}")

        def stop(self):
            self.current_speed = 0
            self.current_direction = "stop"
            print(f"MockMotor (pins {self.forward_pin}/{self.backward_pin}) stopped")

        def close(self):
            self.stop()
            print(f"MockMotor (pins {self.forward_pin}/{self.backward_pin}) closed")

    Motor = MockMotor

from src.iot.thing import Thing, Parameter, ValueType
from src.utils.logging_config import get_logger

logger = get_logger(__name__)


class MotorController(Thing):
    """L298N电机控制器

    使用gpiozero库控制两个直流电机，支持方向、速度和定时控制
    GPIO连接：ENA=17, ENB=18, IN1=27, IN2=22, IN3=23, IN4=24
    """

    def __init__(self):
        super().__init__("MotorController", "L298N双电机控制器")

        # 电机状态
        self.current_speed = 0  # 当前速度 0-100
        self.current_direction = "stop"  # 当前方向: forward, backward, left, right, stop
        self.is_initialized = False
        self.is_moving = False

        # 定时器相关
        self.movement_timer = None
        self.timer_lock = threading.Lock()

        # 初始化电机
        self._initialize_motors()
        
        # 添加属性
        self.add_property("current_speed", "当前速度(0-100)", self.get_current_speed)
        self.add_property("current_direction", "当前方向", self.get_current_direction)
        self.add_property("is_moving", "是否正在运动", self.get_is_moving)
        self.add_property("is_initialized", "是否已初始化", self.get_is_initialized)
        
        # 添加方法
        self.add_method(
            "SetSpeed",
            "设置速度",
            [Parameter("speed", "速度(0-100)", ValueType.NUMBER, True)],
            self._set_speed
        )

        self.add_method(
            "MoveForward",
            "向前移动",
            [
                Parameter("speed", "速度(0-100)", ValueType.NUMBER, False),
                Parameter("duration", "持续时间(秒)，0表示持续运动", ValueType.NUMBER, False)
            ],
            self._move_forward
        )

        self.add_method(
            "MoveBackward",
            "向后移动",
            [
                Parameter("speed", "速度(0-100)", ValueType.NUMBER, False),
                Parameter("duration", "持续时间(秒)，0表示持续运动", ValueType.NUMBER, False)
            ],
            self._move_backward
        )

        self.add_method(
            "TurnLeft",
            "向左转",
            [
                Parameter("speed", "速度(0-100)", ValueType.NUMBER, False),
                Parameter("duration", "持续时间(秒)，0表示持续运动", ValueType.NUMBER, False)
            ],
            self._turn_left
        )

        self.add_method(
            "TurnRight",
            "向右转",
            [
                Parameter("speed", "速度(0-100)", ValueType.NUMBER, False),
                Parameter("duration", "持续时间(秒)，0表示持续运动", ValueType.NUMBER, False)
            ],
            self._turn_right
        )

        self.add_method(
            "StopMovement",
            "停止运动",
            [],
            self._stop_movement
        )
    
    def _initialize_motors(self):
        """初始化电机"""
        try:
            # 使用gpiozero库初始化电机
            # 电机A：左电机，电机B：右电机
            self.motor_a = Motor(forward=27, backward=22, enable=17, pwm=True)  # 左电机
            self.motor_b = Motor(forward=23, backward=24, enable=18, pwm=True)  # 右电机

            # 初始状态：停止
            self.motor_a.stop()
            self.motor_b.stop()

            self.is_initialized = True
            logger.info("电机控制器初始化成功 (使用gpiozero)")

        except Exception as e:
            logger.error(f"电机初始化失败: {e}")
            self.is_initialized = False
    
    def _stop_movement_timer(self):
        """停止运动定时器"""
        with self.timer_lock:
            if self.movement_timer:
                self.movement_timer.cancel()
                self.movement_timer = None

    def _apply_motor_control(self, direction: str, speed: int, duration: float = 0):
        """应用电机控制"""
        if not self.is_initialized:
            logger.warning("电机未初始化")
            return False

        # 停止之前的定时器
        self._stop_movement_timer()

        # 限制速度范围
        speed = max(0, min(100, speed))
        if speed == 0:
            direction = "stop"

        # 将百分比转换为PWM占空比
        pwm_value = speed / 100.0

        # 更新状态
        self.current_direction = direction
        self.current_speed = speed
        self.is_moving = (direction != "stop")

        try:
            if direction == "forward":
                # 两个电机都前进
                self.motor_a.forward(speed=pwm_value)
                self.motor_b.forward(speed=pwm_value)
                logger.info(f"电机前进，速度：{speed}%")
            elif direction == "backward":
                # 两个电机都后退
                self.motor_a.backward(speed=pwm_value)
                self.motor_b.backward(speed=pwm_value)
                logger.info(f"电机后退，速度：{speed}%")
            elif direction == "left":
                # 左转：左电机后退，右电机前进
                self.motor_a.backward(speed=pwm_value)  # 左电机后退
                self.motor_b.forward(speed=pwm_value)   # 右电机前进
                logger.info(f"左转，速度：{speed}%")
            elif direction == "right":
                # 右转：左电机前进，右电机后退
                self.motor_a.forward(speed=pwm_value)   # 左电机前进
                self.motor_b.backward(speed=pwm_value)  # 右电机后退
                logger.info(f"右转，速度：{speed}%")
            else:  # stop
                self.motor_a.stop()
                self.motor_b.stop()
                self.is_moving = False
                logger.info("电机停止")

            # 如果设置了持续时间，启动定时器
            if duration > 0 and direction != "stop":
                with self.timer_lock:
                    self.movement_timer = threading.Timer(duration, self._auto_stop)
                    self.movement_timer.start()
                    logger.info(f"设置定时停止：{duration}秒后自动停止")

            return True

        except Exception as e:
            logger.error(f"电机控制失败: {e}")
            return False

    def _auto_stop(self):
        """自动停止（定时器回调）"""
        logger.info("定时器触发，自动停止电机")
        self._apply_motor_control("stop", 0)
    
    # 属性获取方法
    async def get_current_speed(self):
        return self.current_speed

    async def get_current_direction(self):
        return self.current_direction

    async def get_is_moving(self):
        return self.is_moving

    async def get_is_initialized(self):
        return self.is_initialized
    
    # 控制方法
    async def _set_speed(self, params: Dict[str, Parameter]) -> Dict[str, Any]:
        """设置速度（独立于方向）"""
        speed = params["speed"].get_value()

        if not isinstance(speed, (int, float)) or speed < 0 or speed > 100:
            return {"status": "error", "message": "速度必须在0-100之间"}

        speed = int(speed)

        if speed == 0:
            # 速度为0时停止
            success = self._apply_motor_control("stop", 0)
            message = "电机已停止"
        else:
            # 如果当前有方向，应用新速度
            if self.current_direction != "stop":
                success = self._apply_motor_control(self.current_direction, speed)
                message = f"速度设置为{speed}%，方向：{self.current_direction}"
            else:
                # 只更新速度，不启动电机
                self.current_speed = speed
                success = True
                message = f"速度设置为{speed}%（需要设置方向才能启动）"

        return {
            "status": "success" if success else "error",
            "message": message
        }

    async def _stop_movement(self, params: Dict[str, Parameter]) -> Dict[str, Any]:
        """停止运动"""
        success = self._apply_motor_control("stop", 0)
        return {
            "status": "success" if success else "error",
            "message": "运动已停止" if success else "停止失败"
        }
    
    async def _move_forward(self, params: Dict[str, Parameter]) -> Dict[str, Any]:
        """向前移动"""
        speed = params.get("speed", Parameter("speed", "", ValueType.NUMBER)).get_value() or 50
        duration = params.get("duration", Parameter("duration", "", ValueType.NUMBER)).get_value() or 0

        speed = int(speed)
        duration = float(duration)

        success = self._apply_motor_control("forward", speed, duration)

        if duration > 0:
            message = f"向前移动，速度{speed}%，持续{duration}秒"
        else:
            message = f"向前移动，速度{speed}%（持续运动）"

        return {
            "status": "success" if success else "error",
            "message": message
        }

    async def _move_backward(self, params: Dict[str, Parameter]) -> Dict[str, Any]:
        """向后移动"""
        speed = params.get("speed", Parameter("speed", "", ValueType.NUMBER)).get_value() or 50
        duration = params.get("duration", Parameter("duration", "", ValueType.NUMBER)).get_value() or 0

        speed = int(speed)
        duration = float(duration)

        success = self._apply_motor_control("backward", speed, duration)

        if duration > 0:
            message = f"向后移动，速度{speed}%，持续{duration}秒"
        else:
            message = f"向后移动，速度{speed}%（持续运动）"

        return {
            "status": "success" if success else "error",
            "message": message
        }

    async def _turn_left(self, params: Dict[str, Parameter]) -> Dict[str, Any]:
        """向左转"""
        speed = params.get("speed", Parameter("speed", "", ValueType.NUMBER)).get_value() or 50
        duration = params.get("duration", Parameter("duration", "", ValueType.NUMBER)).get_value() or 0

        speed = int(speed)
        duration = float(duration)

        success = self._apply_motor_control("left", speed, duration)

        if duration > 0:
            message = f"向左转，速度{speed}%，持续{duration}秒"
        else:
            message = f"向左转，速度{speed}%（持续运动）"

        return {
            "status": "success" if success else "error",
            "message": message
        }

    async def _turn_right(self, params: Dict[str, Parameter]) -> Dict[str, Any]:
        """向右转"""
        speed = params.get("speed", Parameter("speed", "", ValueType.NUMBER)).get_value() or 50
        duration = params.get("duration", Parameter("duration", "", ValueType.NUMBER)).get_value() or 0

        speed = int(speed)
        duration = float(duration)

        success = self._apply_motor_control("right", speed, duration)

        if duration > 0:
            message = f"向右转，速度{speed}%，持续{duration}秒"
        else:
            message = f"向右转，速度{speed}%（持续运动）"

        return {
            "status": "success" if success else "error",
            "message": message
        }
    
    def __del__(self):
        """析构函数，清理资源"""
        if self.is_initialized:
            try:
                # 停止定时器
                self._stop_movement_timer()

                # 停止电机
                if hasattr(self, 'motor_a'):
                    self.motor_a.stop()
                    self.motor_a.close()
                if hasattr(self, 'motor_b'):
                    self.motor_b.stop()
                    self.motor_b.close()

                logger.info("电机资源已清理")
            except Exception as e:
                logger.error(f"资源清理失败: {e}")
