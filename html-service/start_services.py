#!/usr/bin/env python3
"""
启动前后端服务的脚本
同时启动电机控制后端服务(端口5000)和前端服务(端口8888)
"""

import subprocess
import sys
import os
import time
import signal
import threading

class ServiceManager:
    def __init__(self):
        self.processes = []
        self.running = True
        
        # 设置信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """信号处理器，用于优雅退出"""
        print("\n正在关闭所有服务...")
        self.stop_all_services()
        sys.exit(0)
    
    def start_backend(self):
        """启动后端服务"""
        backend_path = os.path.join(os.path.dirname(__file__), 'service', 'motor_control.py')
        
        if not os.path.exists(backend_path):
            print(f"错误：后端文件不存在 {backend_path}")
            return None
        
        try:
            print("启动后端服务 (端口 5000)...")
            process = subprocess.Popen([
                sys.executable, backend_path
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            self.processes.append(('backend', process))
            print("✓ 后端服务启动成功")
            return process
        except Exception as e:
            print(f"✗ 后端服务启动失败: {e}")
            return None
    
    def start_frontend(self):
        """启动前端服务"""
        frontend_path = os.path.join(os.path.dirname(__file__), 'html', 'server.py')
        
        if not os.path.exists(frontend_path):
            print(f"错误：前端文件不存在 {frontend_path}")
            return None
        
        try:
            print("启动前端服务 (端口 8888)...")
            process = subprocess.Popen([
                sys.executable, frontend_path
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            self.processes.append(('frontend', process))
            print("✓ 前端服务启动成功")
            return process
        except Exception as e:
            print(f"✗ 前端服务启动失败: {e}")
            return None
    
    def check_dependencies(self):
        """检查依赖"""
        print("检查依赖...")
        
        try:
            import flask
            import flask_cors
            import gpiozero
            print("✓ 所有依赖已安装")
            return True
        except ImportError as e:
            print(f"✗ 缺少依赖: {e}")
            print("请安装依赖:")
            print("  pip3 install flask flask-cors gpiozero")
            return False
    
    def monitor_processes(self):
        """监控进程状态"""
        while self.running:
            for name, process in self.processes:
                if process.poll() is not None:
                    print(f"警告：{name}服务已停止")
                    # 读取错误输出
                    stderr = process.stderr.read()
                    if stderr:
                        print(f"{name}错误输出: {stderr}")
            time.sleep(5)
    
    def stop_all_services(self):
        """停止所有服务"""
        self.running = False
        
        for name, process in self.processes:
            if process.poll() is None:  # 进程仍在运行
                print(f"正在停止{name}服务...")
                process.terminate()
                
                # 等待进程结束
                try:
                    process.wait(timeout=5)
                    print(f"✓ {name}服务已停止")
                except subprocess.TimeoutExpired:
                    print(f"强制终止{name}服务...")
                    process.kill()
                    process.wait()
    
    def get_local_ip(self):
        """获取本地IP地址"""
        try:
            import socket
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except:
            return "localhost"
    
    def start_all_services(self):
        """启动所有服务"""
        print("=" * 50)
        print("🚗 电机控制系统启动器")
        print("=" * 50)
        
        # 检查依赖
        if not self.check_dependencies():
            return False
        
        # 启动后端服务
        backend_process = self.start_backend()
        if not backend_process:
            return False
        
        # 等待后端启动
        time.sleep(2)
        
        # 启动前端服务
        frontend_process = self.start_frontend()
        if not frontend_process:
            self.stop_all_services()
            return False
        
        # 等待前端启动
        time.sleep(2)
        
        # 显示访问信息
        local_ip = self.get_local_ip()
        print("\n" + "=" * 50)
        print("🎉 所有服务启动成功！")
        print("=" * 50)
        print(f"前端地址: http://{local_ip}:8888")
        print(f"后端API: http://{local_ip}:5000/api")
        print("\n📱 手机访问:")
        print(f"   在手机浏览器中打开: http://{local_ip}:8888")
        print("\n💡 提示:")
        print("   - 确保手机和树莓派在同一网络")
        print("   - 按 Ctrl+C 停止所有服务")
        print("=" * 50)
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=self.monitor_processes, daemon=True)
        monitor_thread.start()
        
        return True

def main():
    """主函数"""
    service_manager = ServiceManager()
    
    try:
        if service_manager.start_all_services():
            # 保持主线程运行
            while service_manager.running:
                time.sleep(1)
        else:
            print("服务启动失败")
            sys.exit(1)
    except KeyboardInterrupt:
        service_manager.signal_handler(signal.SIGINT, None)
    except Exception as e:
        print(f"程序运行出错: {e}")
        service_manager.stop_all_services()
        sys.exit(1)

if __name__ == "__main__":
    main()
