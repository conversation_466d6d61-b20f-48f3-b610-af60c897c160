#!/usr/bin/env python3
"""
L298N电机驱动HTTP后端服务
使用树莓派5控制L298N电机驱动模块
GPIO连接：ENA=17, ENB=18, IN1=27, IN2=22, IN3=23, IN4=24
"""

from gpiozero import Motor, OutputDevice
from flask import Flask, request, jsonify
from flask_cors import CORS
import signal
import sys
import threading
import time

app = Flask(__name__)
CORS(app)  # 允许跨域请求

class MotorController:
    def __init__(self):
        """
        初始化L298N电机控制器

        GPIO连接配置：
        - ENA (Enable A) = GPIO 17
        - ENB (Enable B) = GPIO 18
        - IN1 = GPIO 27
        - IN2 = GPIO 22
        - IN3 = GPIO 23
        - IN4 = GPIO 24

        步进电机使用相同的IN1-IN4引脚控制
        """
        # 根据您提供的示例和GPIO连接，配置两个电机
        # 电机A：使用ENA(17)作为enable，IN1(27)和IN2(22)控制方向
        self.motor_a = Motor(forward=27, backward=22, enable=17, pwm=True)

        # 电机B：使用ENB(18)作为enable，IN3(23)和IN4(24)控制方向
        self.motor_b = Motor(forward=23, backward=24, enable=18, pwm=True)

        # 步进电机控制引脚 - 复用直流电机的IN1-IN4引脚
        self.stepper_in1 = OutputDevice(27)  # IN1
        self.stepper_in2 = OutputDevice(22)  # IN2
        self.stepper_in3 = OutputDevice(23)  # IN3
        self.stepper_in4 = OutputDevice(24)  # IN4

        # 步进电机参数
        self.steps_per_revolution = 64 * 64  # 28BYJ-48步进电机：64步/圈 * 64:1减速比
        self.step_sequence = [
            [1, 0, 0, 0],
            [1, 1, 0, 0],
            [0, 1, 0, 0],
            [0, 1, 1, 0],
            [0, 0, 1, 0],
            [0, 0, 1, 1],
            [0, 0, 0, 1],
            [1, 0, 0, 1]
        ]
        self.step_delay = 0.001  # 步进延时（秒）

        self.running = True
        self.current_speed = 0  # 当前速度百分比 (0-100)
        self.current_direction = "stop"  # 当前方向

        # 设置信号处理器，用于优雅退出
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

        print("L298N电机控制器已初始化")
        print("GPIO连接配置：")
        print("  ENA (Enable A) = GPIO 17")
        print("  ENB (Enable B) = GPIO 18")
        print("  IN1 = GPIO 27 (步进电机IN1)")
        print("  IN2 = GPIO 22 (步进电机IN2)")
        print("  IN3 = GPIO 23 (步进电机IN3)")
        print("  IN4 = GPIO 24 (步进电机IN4)")
        print("  步进电机：28BYJ-48 (5.626°/64步)")
        print("  步进电机减速比：64:1")
    
    def signal_handler(self, signum, frame):
        """信号处理器，用于优雅退出程序"""
        print("\n正在停止电机...")
        self.stop_motors()
        self.running = False
        sys.exit(0)

    def stop_motors(self):
        """停止所有电机"""
        self.motor_a.stop()
        self.motor_b.stop()
        self.current_speed = 0
        self.current_direction = "stop"
        print("电机已停止")

    def set_speed(self, speed_percent):
        """
        设置电机速度（独立于方向）

        Args:
            speed_percent (int): 速度百分比 0-100，0为停止
        """
        if speed_percent == 0:
            self.current_speed = 0
            self.stop_motors()
            return True

        if not (20 <= speed_percent <= 100):
            print("错误：速度必须在20%-100%之间")
            return False

        self.current_speed = speed_percent
        print(f"设置速度：{speed_percent}%")

        # 如果当前有方向，立即应用新速度
        if self.current_direction != "stop":
            self._apply_motor_control()

        return True

    def set_direction(self, direction):
        """
        设置电机方向（独立于速度）

        Args:
            direction (str): 方向 "forward", "backward", "left", "right", "stop"
        """
        if direction == "stop":
            self.current_direction = "stop"
            self.stop_motors()
            return True

        if direction not in ["forward", "backward", "left", "right"]:
            print("错误：方向必须是 'forward', 'backward', 'left', 'right', 'stop'")
            return False

        self.current_direction = direction
        print(f"设置方向：{direction}")

        # 如果当前有速度，立即应用新方向
        if self.current_speed > 0:
            self._apply_motor_control()

        return True

    def _apply_motor_control(self):
        """
        应用当前的速度和方向设置到电机
        确保两个电机的PWM始终一致，只是方向不同
        """
        if self.current_speed == 0 or self.current_direction == "stop":
            self.stop_motors()
            return

        # 将百分比转换为PWM占空比
        pwm_value = self.current_speed / 100.0

        if self.current_direction == "forward":
            # 两个电机都前进，PWM相同
            self.motor_a.forward(speed=pwm_value)
            self.motor_b.forward(speed=pwm_value)
            print(f"电机前进，速度：{self.current_speed}% (PWM: {pwm_value:.2f})")
        elif self.current_direction == "backward":
            # 两个电机都后退，PWM相同
            self.motor_a.backward(speed=pwm_value)
            self.motor_b.backward(speed=pwm_value)
            print(f"电机后退，速度：{self.current_speed}% (PWM: {pwm_value:.2f})")
        elif self.current_direction == "left":
            # 左转：左电机后退，右电机前进，PWM相同
            self.motor_a.backward(speed=pwm_value)  # 左电机后退
            self.motor_b.forward(speed=pwm_value)   # 右电机前进
            print(f"左转，速度：{self.current_speed}% (PWM: {pwm_value:.2f})")
        elif self.current_direction == "right":
            # 右转：左电机前进，右电机后退，PWM相同
            self.motor_a.forward(speed=pwm_value)   # 左电机前进
            self.motor_b.backward(speed=pwm_value)  # 右电机后退
            print(f"右转，速度：{self.current_speed}% (PWM: {pwm_value:.2f})")
    
    def get_status(self):
        """获取当前电机状态"""
        return {
            "direction": self.current_direction,
            "speed": self.current_speed,
            "running": self.running
        }

    def is_dc_motor_running(self):
        """检查直流电机是否在运动"""
        return self.current_direction != "stop" and self.current_speed > 0

    def stepper_step(self, step_index):
        """执行一步步进电机动作"""
        step = self.step_sequence[step_index]
        self.stepper_in1.value = step[0]
        self.stepper_in2.value = step[1]
        self.stepper_in3.value = step[2]
        self.stepper_in4.value = step[3]
        time.sleep(self.step_delay)

    def stepper_stop(self):
        """停止步进电机（所有引脚设为低电平）"""
        self.stepper_in1.off()
        self.stepper_in2.off()
        self.stepper_in3.off()
        self.stepper_in4.off()

    def rotate_stepper(self, revolutions, clockwise=True):
        """
        转动步进电机指定圈数

        Args:
            revolutions (int): 转动圈数
            clockwise (bool): True为顺时针，False为逆时针
        """
        # 检查直流电机是否在运动
        if self.is_dc_motor_running():
            return False, "请先停止直流电机运动"

        total_steps = int(revolutions * self.steps_per_revolution)
        step_count = 0

        print(f"步进电机开始转动 {revolutions} 圈 ({'顺时针' if clockwise else '逆时针'})")
        print(f"总步数: {total_steps}")

        try:
            for i in range(total_steps):
                if clockwise:
                    step_index = i % len(self.step_sequence)
                else:
                    step_index = (len(self.step_sequence) - 1 - (i % len(self.step_sequence)))

                self.stepper_step(step_index)
                step_count += 1

                # 每1000步打印一次进度
                if step_count % 1000 == 0:
                    progress = (step_count / total_steps) * 100
                    print(f"步进电机转动进度: {progress:.1f}%")

            # 停止步进电机
            self.stepper_stop()
            print(f"步进电机转动完成，共转动 {step_count} 步")
            return True, f"步进电机转动完成 {revolutions} 圈"

        except Exception as e:
            self.stepper_stop()
            print(f"步进电机转动出错: {e}")
            return False, f"步进电机转动失败: {str(e)}"

    def cleanup(self):
        """清理资源"""
        self.stop_motors()
        self.stepper_stop()
        self.motor_a.close()
        self.motor_b.close()
        self.stepper_in1.close()
        self.stepper_in2.close()
        self.stepper_in3.close()
        self.stepper_in4.close()

# 创建全局电机控制器实例
motor_controller = MotorController()

# API路由
@app.route('/api/motor/speed', methods=['POST'])
def set_motor_speed():
    """设置电机速度API"""
    try:
        data = request.get_json()
        speed = data.get('speed', 0)

        success = motor_controller.set_speed(speed)

        if success:
            return jsonify({
                "status": "success",
                "message": f"速度设置为{speed}%",
                "motor_status": motor_controller.get_status()
            })
        else:
            return jsonify({"status": "error", "message": "速度设置失败"}), 400

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/motor/direction', methods=['POST'])
def set_motor_direction():
    """设置电机方向API"""
    try:
        data = request.get_json()
        direction = data.get('direction', 'stop')

        success = motor_controller.set_direction(direction)

        if success:
            direction_map = {
                'forward': '前进',
                'backward': '后退',
                'left': '左转',
                'right': '右转',
                'stop': '停止'
            }
            message = f"方向设置为{direction_map.get(direction, direction)}"
            return jsonify({
                "status": "success",
                "message": message,
                "motor_status": motor_controller.get_status()
            })
        else:
            return jsonify({"status": "error", "message": "方向设置失败"}), 400

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/motor/status', methods=['GET'])
def get_motor_status():
    """获取电机状态API"""
    try:
        status = motor_controller.get_status()
        return jsonify({"status": "success", "data": status})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/motor/stop', methods=['POST'])
def stop_motor():
    """停止电机API"""
    try:
        motor_controller.stop_motors()
        return jsonify({"status": "success", "message": "电机已停止"})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/door/open', methods=['POST'])
def open_door():
    """开门API - 步进电机顺时针转5圈"""
    try:
        success, message = motor_controller.rotate_stepper(5, clockwise=True)

        if success:
            return jsonify({
                "status": "success",
                "message": "门已打开 - " + message
            })
        else:
            return jsonify({
                "status": "error",
                "message": message
            }), 400

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/door/close', methods=['POST'])
def close_door():
    """关门API - 步进电机逆时针转5圈"""
    try:
        success, message = motor_controller.rotate_stepper(5, clockwise=False)

        if success:
            return jsonify({
                "status": "success",
                "message": "门已关闭 - " + message
            })
        else:
            return jsonify({
                "status": "error",
                "message": message
            }), 400

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

def main():
    """主函数 - 启动HTTP服务器"""
    print("L298N电机驱动HTTP后端服务")
    print("=" * 40)
    print("服务器启动中...")
    print("API端点：")
    print("  POST /api/motor/speed     - 设置速度")
    print("  POST /api/motor/direction - 设置方向")
    print("  GET  /api/motor/status    - 获取状态")
    print("  POST /api/motor/stop      - 停止电机")
    print("=" * 40)

    try:
        # 启动Flask服务器
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n正在关闭服务器...")
    except Exception as e:
        print(f"服务器运行出错: {e}")
    finally:
        motor_controller.cleanup()
        print("服务器已关闭")

if __name__ == "__main__":
    main()
