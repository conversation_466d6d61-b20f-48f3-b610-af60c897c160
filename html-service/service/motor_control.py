#!/usr/bin/env python3
"""
L298N电机驱动HTTP后端服务
使用树莓派5控制L298N电机驱动模块
GPIO连接：ENA=17, ENB=18, IN1=27, IN2=22, IN3=23, IN4=24
"""

from gpiozero import Motor, OutputDevice
from flask import Flask, request, jsonify
from flask_cors import CORS
import signal
import sys
import threading
import time

app = Flask(__name__)
CORS(app)  # 允许跨域请求

class StepperMotor:
    def __init__(self, motor_controller):
        """
        初始化28BYJ-48步进电机控制器
        使用与直流电机相同的L298N模块，复用GPIO引脚

        Args:
            motor_controller: 主电机控制器实例，用于访问GPIO引脚
        """
        self.motor_controller = motor_controller

        # 28BYJ-48步进电机的步进序列（半步模式）
        # 使用L298N的IN1, IN2, IN3, IN4引脚
        self.step_sequence = [
            [1, 0, 0, 0],  # IN1=1, IN2=0, IN3=0, IN4=0
            [1, 1, 0, 0],  # IN1=1, IN2=1, IN3=0, IN4=0
            [0, 1, 0, 0],  # IN1=0, IN2=1, IN3=0, IN4=0
            [0, 1, 1, 0],  # IN1=0, IN2=1, IN3=1, IN4=0
            [0, 0, 1, 0],  # IN1=0, IN2=0, IN3=1, IN4=0
            [0, 0, 1, 1],  # IN1=0, IN2=0, IN3=1, IN4=1
            [0, 0, 0, 1],  # IN1=0, IN2=0, IN3=0, IN4=1
            [1, 0, 0, 1]   # IN1=1, IN2=0, IN3=0, IN4=1
        ]

        self.current_step = 0
        # 28BYJ-48参数：步进角度5.626度/64，减速比64:1
        # 一圈需要的步数 = 360 / 5.626 * 64 = 4096步（半步模式）
        self.steps_per_revolution = 4096

    def step_forward(self, steps=1):
        """向前步进指定步数"""
        for _ in range(steps):
            self._set_step(self.current_step)
            self.current_step = (self.current_step + 1) % len(self.step_sequence)
            time.sleep(0.002)  # 2ms延时，控制步进速度

    def step_backward(self, steps=1):
        """向后步进指定步数"""
        for _ in range(steps):
            self.current_step = (self.current_step - 1) % len(self.step_sequence)
            self._set_step(self.current_step)
            time.sleep(0.002)  # 2ms延时，控制步进速度

    def _set_step(self, step):
        """设置步进电机的当前步，使用L298N的IN1-IN4引脚"""
        sequence = self.step_sequence[step]

        # 使用L298N的IN1, IN2, IN3, IN4引脚控制步进电机
        # IN1 = GPIO 27, IN2 = GPIO 22, IN3 = GPIO 23, IN4 = GPIO 24
        from gpiozero import OutputDevice

        # 临时创建GPIO控制对象
        in1 = OutputDevice(27)
        in2 = OutputDevice(22)
        in3 = OutputDevice(23)
        in4 = OutputDevice(24)

        pins = [in1, in2, in3, in4]

        try:
            for i, pin in enumerate(pins):
                if sequence[i]:
                    pin.on()
                else:
                    pin.off()
        finally:
            # 清理临时GPIO对象
            for pin in pins:
                pin.close()

    def rotate_revolutions(self, revolutions, direction='forward'):
        """旋转指定圈数"""
        total_steps = int(revolutions * self.steps_per_revolution)
        print(f"步进电机旋转{revolutions}圈，总步数：{total_steps}")

        if direction == 'forward':
            self.step_forward(total_steps)
        else:
            self.step_backward(total_steps)

        # 旋转完成后关闭所有引脚
        self.stop()

    def stop(self):
        """停止步进电机，关闭所有引脚"""
        from gpiozero import OutputDevice

        # 临时创建GPIO控制对象来关闭所有引脚
        pins = [OutputDevice(27), OutputDevice(22), OutputDevice(23), OutputDevice(24)]
        try:
            for pin in pins:
                pin.off()
        finally:
            for pin in pins:
                pin.close()

    def cleanup(self):
        """清理资源"""
        self.stop()

class MotorController:
    def __init__(self):
        """
        初始化L298N电机控制器和步进电机控制器

        GPIO连接配置：
        L298N模块（同时控制直流电机和步进电机）：
        - ENA (Enable A) = GPIO 17
        - ENB (Enable B) = GPIO 18
        - IN1 = GPIO 27
        - IN2 = GPIO 22
        - IN3 = GPIO 23
        - IN4 = GPIO 24

        注意：28BYJ-48步进电机复用IN1-IN4引脚，与直流电机不能同时运行
        """
        # 根据您提供的示例和GPIO连接，配置两个直流电机
        # 电机A：使用ENA(17)作为enable，IN1(27)和IN2(22)控制方向
        self.motor_a = Motor(forward=27, backward=22, enable=17, pwm=True)

        # 电机B：使用ENB(18)作为enable，IN3(23)和IN4(24)控制方向
        self.motor_b = Motor(forward=23, backward=24, enable=18, pwm=True)

        # 初始化步进电机（用于开门/关门，复用L298N模块）
        self.stepper_motor = StepperMotor(self)

        self.running = True
        self.current_speed = 20  # 当前速度百分比 (20-100)
        self.current_direction = "stop"  # 当前方向

        # 设置信号处理器，用于优雅退出
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

        print("L298N电机控制器已初始化")
        print("GPIO连接配置：")
        print("直流电机：")
        print("  ENA (Enable A) = GPIO 17")
        print("  ENB (Enable B) = GPIO 18")
        print("  IN1 = GPIO 27")
        print("  IN2 = GPIO 22")
        print("  IN3 = GPIO 23")
        print("  IN4 = GPIO 24")
        print("步进电机：")
        print("  引脚1 = GPIO 5")
        print("  引脚2 = GPIO 6")
        print("  引脚3 = GPIO 13")
        print("  引脚4 = GPIO 19")
    
    def signal_handler(self, signum, frame):
        """信号处理器，用于优雅退出程序"""
        print("\n正在停止电机...")
        self.stop_motors()
        self.running = False
        sys.exit(0)

    def stop_motors(self):
        """停止所有电机"""
        self.motor_a.stop()
        self.motor_b.stop()
        self.current_speed = 0
        self.current_direction = "stop"
        print("电机已停止")

    def set_speed(self, speed_percent):
        """
        设置电机速度（独立于方向）

        Args:
            speed_percent (int): 速度百分比 0-100，0为停止
        """
        if speed_percent == 0:
            self.current_speed = 0
            self.stop_motors()
            return True

        if not (20 <= speed_percent <= 100):
            print("错误：速度必须在20%-100%之间")
            return False

        self.current_speed = speed_percent
        print(f"设置速度：{speed_percent}%")

        # 如果当前有方向，立即应用新速度
        if self.current_direction != "stop":
            self._apply_motor_control()

        return True

    def set_direction(self, direction):
        """
        设置电机方向（独立于速度）

        Args:
            direction (str): 方向 "forward", "backward", "left", "right", "stop"
        """
        if direction == "stop":
            self.current_direction = "stop"
            self.stop_motors()
            return True

        if direction not in ["forward", "backward", "left", "right"]:
            print("错误：方向必须是 'forward', 'backward', 'left', 'right', 'stop'")
            return False

        self.current_direction = direction
        print(f"设置方向：{direction}")

        # 如果当前有速度，立即应用新方向
        if self.current_speed > 0:
            self._apply_motor_control()

        return True

    def _apply_motor_control(self):
        """
        应用当前的速度和方向设置到电机
        确保两个电机的PWM始终一致，只是方向不同
        """
        if self.current_speed == 0 or self.current_direction == "stop":
            self.stop_motors()
            return

        # 将百分比转换为PWM占空比
        pwm_value = self.current_speed / 100.0

        if self.current_direction == "forward":
            # 两个电机都前进，PWM相同
            self.motor_a.forward(speed=pwm_value)
            self.motor_b.forward(speed=pwm_value)
            print(f"电机前进，速度：{self.current_speed}% (PWM: {pwm_value:.2f})")
        elif self.current_direction == "backward":
            # 两个电机都后退，PWM相同
            self.motor_a.backward(speed=pwm_value)
            self.motor_b.backward(speed=pwm_value)
            print(f"电机后退，速度：{self.current_speed}% (PWM: {pwm_value:.2f})")
        elif self.current_direction == "left":
            # 左转：左电机后退，右电机前进，PWM相同
            self.motor_a.backward(speed=pwm_value)  # 左电机后退
            self.motor_b.forward(speed=pwm_value)   # 右电机前进
            print(f"左转，速度：{self.current_speed}% (PWM: {pwm_value:.2f})")
        elif self.current_direction == "right":
            # 右转：左电机前进，右电机后退，PWM相同
            self.motor_a.forward(speed=pwm_value)   # 左电机前进
            self.motor_b.backward(speed=pwm_value)  # 右电机后退
            print(f"右转，速度：{self.current_speed}% (PWM: {pwm_value:.2f})")
    
    def get_status(self):
        """获取当前电机状态"""
        return {
            "direction": self.current_direction,
            "speed": self.current_speed,
            "running": self.running
        }

    def control_door(self, action):
        """
        控制门的开关（通过步进电机）

        Args:
            action (str): 'open' 或 'close'
        """
        try:
            # 检查直流电机是否在运行
            if self.current_direction != "stop":
                return False, "请先停止运动再开门或者关门"

            if action == 'open':
                print("开门：步进电机正转5圈")
                # 先停止直流电机，确保GPIO引脚可用
                self.stop_motors()
                self.stepper_motor.rotate_revolutions(2, 'forward')
                return True, "门已打开"
            elif action == 'close':
                print("关门：步进电机反转5圈")
                # 先停止直流电机，确保GPIO引脚可用
                self.stop_motors()
                self.stepper_motor.rotate_revolutions(2, 'backward')
                return True, "门已关闭"
            else:
                return False, "无效的门控制动作"
        except Exception as e:
            print(f"门控制失败: {e}")
            return False, f"门控制失败: {str(e)}"

    def cleanup(self):
        """清理资源"""
        self.stop_motors()
        self.motor_a.close()
        self.motor_b.close()
        self.stepper_motor.cleanup()

# 创建全局电机控制器实例
motor_controller = MotorController()

# API路由
@app.route('/api/motor/speed', methods=['POST'])
def set_motor_speed():
    """设置电机速度API"""
    try:
        data = request.get_json()
        speed = data.get('speed', 0)

        success = motor_controller.set_speed(speed)

        if success:
            return jsonify({
                "status": "success",
                "message": f"速度设置为{speed}%",
                "motor_status": motor_controller.get_status()
            })
        else:
            return jsonify({"status": "error", "message": "速度设置失败"}), 400

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/motor/direction', methods=['POST'])
def set_motor_direction():
    """设置电机方向API"""
    try:
        data = request.get_json()
        direction = data.get('direction', 'stop')

        success = motor_controller.set_direction(direction)

        if success:
            direction_map = {
                'forward': '前进',
                'backward': '后退',
                'left': '左转',
                'right': '右转',
                'stop': '停止'
            }
            message = f"方向设置为{direction_map.get(direction, direction)}"
            return jsonify({
                "status": "success",
                "message": message,
                "motor_status": motor_controller.get_status()
            })
        else:
            return jsonify({"status": "error", "message": "方向设置失败"}), 400

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/motor/status', methods=['GET'])
def get_motor_status():
    """获取电机状态API"""
    try:
        status = motor_controller.get_status()
        return jsonify({"status": "success", "data": status})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/motor/stop', methods=['POST'])
def stop_motor():
    """停止电机API"""
    try:
        motor_controller.stop_motors()
        return jsonify({"status": "success", "message": "电机已停止"})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/door/control', methods=['POST'])
def control_door():
    """控制门开关API"""
    try:
        data = request.get_json()
        action = data.get('action', '')

        if action not in ['open', 'close']:
            return jsonify({"status": "error", "message": "无效的门控制动作"}), 400

        success, message = motor_controller.control_door(action)

        if success:
            return jsonify({
                "status": "success",
                "message": message
            })
        else:
            return jsonify({"status": "error", "message": message}), 500

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

def main():
    """主函数 - 启动HTTP服务器"""
    print("L298N电机驱动HTTP后端服务")
    print("=" * 40)
    print("服务器启动中...")
    print("API端点：")
    print("  POST /api/motor/speed     - 设置速度")
    print("  POST /api/motor/direction - 设置方向")
    print("  GET  /api/motor/status    - 获取状态")
    print("  POST /api/motor/stop      - 停止电机")
    print("  POST /api/door/control    - 控制门开关")
    print("=" * 40)

    try:
        # 启动Flask服务器
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n正在关闭服务器...")
    except Exception as e:
        print(f"服务器运行出错: {e}")
    finally:
        motor_controller.cleanup()
        print("服务器已关闭")

if __name__ == "__main__":
    main()
