<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电机控制面板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            width: 100%;
            text-align: center;
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
        }

        .status {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 25px;
            border-left: 4px solid #007bff;
        }

        .status-item {
            margin: 5px 0;
            font-size: 14px;
            color: #666;
        }

        .speed-control {
            margin-bottom: 30px;
        }

        .speed-label {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            display: block;
        }

        .speed-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .speed-btn {
            background: #28a745;
            color: white;
            border: none;
            border-radius: 10px;
            padding: 12px 20px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: none;
            min-width: 80px;
        }

        .speed-btn:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        .speed-btn:active {
            transform: translateY(0);
            background: #1e7e34;
        }

        .speed-btn.decrease {
            background: #ffc107;
        }

        .speed-btn.decrease:hover {
            background: #e0a800;
        }

        .speed-btn.decrease:active {
            background: #d39e00;
        }

        .speed-value {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 15px;
        }

        .door-control {
            margin-bottom: 20px;
        }

        .door-btn {
            background: #17a2b8;
            color: white;
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: none;
            width: 100%;
            max-width: 200px;
        }

        .door-btn:hover {
            background: #138496;
            transform: translateY(-2px);
        }

        .door-btn:active {
            transform: translateY(0);
            background: #117a8b;
        }

        .door-btn.closing {
            background: #dc3545;
        }

        .door-btn.closing:hover {
            background: #c82333;
        }

        .door-btn.closing:active {
            background: #bd2130;
        }

        .direction-controls {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: 1fr 1fr 1fr;
            gap: 10px;
            margin-bottom: 20px;
            max-width: 200px;
            margin-left: auto;
            margin-right: auto;
        }

        .direction-btn {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 10px;
            padding: 15px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: none;
        }

        .direction-btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .direction-btn:active {
            transform: translateY(0);
            background: #004085;
        }

        .direction-btn.forward {
            grid-column: 2;
            grid-row: 1;
        }

        .direction-btn.left {
            grid-column: 1;
            grid-row: 2;
        }

        .direction-btn.stop {
            grid-column: 2;
            grid-row: 2;
            background: #dc3545;
        }

        .direction-btn.stop:hover {
            background: #c82333;
        }

        .direction-btn.right {
            grid-column: 3;
            grid-row: 2;
        }

        .direction-btn.backward {
            grid-column: 2;
            grid-row: 3;
        }

        .message {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 480px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .direction-controls {
                max-width: 180px;
            }
            
            .direction-btn {
                padding: 12px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚗 电机控制面板</h1>
        
        <div class="status">
            <div class="status-item">状态: <span id="status-direction">停止</span></div>
            <div class="status-item">速度: <span id="status-speed">0</span>%</div>
        </div>

        <div class="speed-control">
            <label class="speed-label">速度控制</label>
            <div class="speed-buttons">
                <button class="speed-btn decrease" id="decreaseSpeed">减速</button>
                <button class="speed-btn increase" id="increaseSpeed">加速</button>
            </div>
            <div class="speed-value" id="speedValue">20%</div>
        </div>

        <div class="door-control">
            <button class="door-btn" id="doorBtn">开门</button>
        </div>

        <div class="direction-controls">
            <button class="direction-btn forward" data-direction="forward">↑<br>前进</button>
            <button class="direction-btn left" data-direction="left">←<br>左转</button>
            <button class="direction-btn stop" data-direction="stop">⏹<br>停止</button>
            <button class="direction-btn right" data-direction="right">→<br>右转</button>
            <button class="direction-btn backward" data-direction="backward">↓<br>后退</button>
        </div>

        <div id="message" class="message" style="display: none;"></div>
    </div>

    <script>
        // API基础URL
        const API_BASE = 'http://' + window.location.hostname + ':5000/api';

        // 获取DOM元素
        const speedValue = document.getElementById('speedValue');
        const statusDirection = document.getElementById('status-direction');
        const statusSpeed = document.getElementById('status-speed');
        const messageDiv = document.getElementById('message');
        const directionBtns = document.querySelectorAll('.direction-btn');
        const increaseSpeedBtn = document.getElementById('increaseSpeed');
        const decreaseSpeedBtn = document.getElementById('decreaseSpeed');
        const doorBtn = document.getElementById('doorBtn');

        // 当前速度值
        let currentSpeed = 20;
        let isDoorOpen = false;

        // 速度按钮事件处理
        increaseSpeedBtn.addEventListener('click', function() {
            if (currentSpeed < 100) {
                currentSpeed = Math.min(100, currentSpeed + 5);
                speedValue.textContent = currentSpeed + '%';
                setMotorSpeed(currentSpeed);
            }
        });

        decreaseSpeedBtn.addEventListener('click', function() {
            if (currentSpeed > 20) {
                currentSpeed = Math.max(20, currentSpeed - 5);
                speedValue.textContent = currentSpeed + '%';
                setMotorSpeed(currentSpeed);
            }
        });

        // 开门/关门按钮事件处理
        doorBtn.addEventListener('click', function() {
            if (isDoorOpen) {
                // 当前是开门状态，点击关门
                controlDoor('close');
            } else {
                // 当前是关门状态，点击开门
                controlDoor('open');
            }
        });

        // 显示消息
        function showMessage(text, type = 'success') {
            messageDiv.textContent = text;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';
            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 3000);
        }

        // 更新状态显示
        function updateStatus(direction, speed) {
            const directionMap = {
                'forward': '前进',
                'backward': '后退',
                'left': '左转',
                'right': '右转',
                'stop': '停止'
            };
            statusDirection.textContent = directionMap[direction] || direction;
            statusSpeed.textContent = speed;
        }

        // 设置电机速度（独立接口）
        async function setMotorSpeed(speed) {
            try {
                const response = await fetch(`${API_BASE}/motor/speed`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        speed: speed
                    })
                });

                const data = await response.json();

                if (data.status === 'success') {
                    showMessage(data.message, 'success');
                    if (data.motor_status) {
                        updateStatus(data.motor_status.direction, data.motor_status.speed);
                    }
                } else {
                    showMessage(data.message, 'error');
                }
            } catch (error) {
                showMessage('速度设置失败: ' + error.message, 'error');
            }
        }

        // 设置电机方向（独立接口）
        async function setMotorDirection(direction) {
            try {
                const response = await fetch(`${API_BASE}/motor/direction`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        direction: direction
                    })
                });

                const data = await response.json();

                if (data.status === 'success') {
                    showMessage(data.message, 'success');
                    if (data.motor_status) {
                        updateStatus(data.motor_status.direction, data.motor_status.speed);
                    }
                } else {
                    showMessage(data.message, 'error');
                }
            } catch (error) {
                showMessage('方向设置失败: ' + error.message, 'error');
            }
        }

        // 控制开门/关门
        async function controlDoor(action) {
            try {
                const response = await fetch(`${API_BASE}/door/control`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: action
                    })
                });

                const data = await response.json();

                if (data.status === 'success') {
                    showMessage(data.message, 'success');
                    // 更新按钮状态
                    if (action === 'open') {
                        isDoorOpen = true;
                        doorBtn.textContent = '关门';
                        doorBtn.classList.add('closing');
                    } else {
                        isDoorOpen = false;
                        doorBtn.textContent = '开门';
                        doorBtn.classList.remove('closing');
                    }
                } else {
                    showMessage(data.message, 'error');
                }
            } catch (error) {
                showMessage('门控制失败: ' + error.message, 'error');
            }
        }

        // 获取电机状态
        async function getMotorStatus() {
            try {
                const response = await fetch(`${API_BASE}/motor/status`);
                const data = await response.json();

                if (data.status === 'success') {
                    updateStatus(data.data.direction, data.data.speed);
                    // 同步速度显示
                    if (data.data.speed !== currentSpeed) {
                        currentSpeed = data.data.speed;
                        speedValue.textContent = currentSpeed + '%';
                    }
                }
            } catch (error) {
                console.log('获取状态失败:', error);
            }
        }

        // 绑定方向按钮事件
        directionBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const direction = this.getAttribute('data-direction');
                setMotorDirection(direction);
            });
        });

        // 页面加载时获取初始状态
        window.addEventListener('load', function() {
            getMotorStatus();
            // 每5秒更新一次状态
            setInterval(getMotorStatus, 5000);
        });
    </script>
</body>
</html>
